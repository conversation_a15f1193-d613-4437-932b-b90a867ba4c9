<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>grid</title>
</head>
<body>

<template id="grid1col" data-component-width="fullscreen"
><div class="corporate_grid">
    <div class="corporate_grid_full">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
</div></template>

<template id="grid2cols" data-component-width="fullscreen"
><div class="corporate_grid">
    <div class="corporate_grid_halfLeft">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
    <div class="corporate_grid_halfRight">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
</div></template>

<template id="grid2cols_b" data-component-width="fullscreen"
><div class="corporate_grid">
    <div class="col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-6 lg:col-end-9">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
    <div class="col-start-1 md:col-start-6 lg:col-start-9 col-end-5 md:col-end-9 lg:col-end-13">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
</div></template>

<template id="grid2cols_c" data-component-width="fullscreen"
><div class="corporate_grid">
    <div class="col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-4 lg:col-end-5">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
    <div class="col-start-1 md:col-start-4 lg:col-start-5 col-end-5 md:col-end-9 lg:col-end-13">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
</div></template>

<template id="grid3cols" data-component-width="fullscreen"
><div class="corporate_grid">
    <div class="corporate_grid_full corporate_grid_flex3Cols">
        <div class="corporate_grid_flexCol">
            <!-- content start -->
            <div class="layoutPlaceholder">Content</div>
            <!-- content end -->
        </div>
        <div class="corporate_grid_flexCol">
            <!-- content start -->
            <div class="layoutPlaceholder">Content</div>
            <!-- content end -->
        </div>
        <div class="corporate_grid_flexCol">
            <!-- content start -->
            <div class="layoutPlaceholder">Content</div>
            <!-- content end -->
        </div>
    </div>
</div></template>

<template id="grid4cols" data-component-width="fullscreen"
><div class="corporate_grid">
    <div class="col-start-1 md:col-start-1 lg:col-start-1 col-end-3 md:col-end-3 lg:col-end-4">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
    <div class="col-start-3 md:col-start-3 lg:col-start-4 col-end-5 md:col-end-5 lg:col-end-7">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
    <div class="col-start-1 md:col-start-5 lg:col-start-7 col-end-3 md:col-end-7 lg:col-end-10">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
    <div class="col-start-3 md:col-start-7 lg:col-start-10 col-end-5 md:col-end-9 lg:col-end-13">
        <!-- content start -->
        <div class="layoutPlaceholder">Content</div>
        <!-- content end -->
    </div>
</div></template>

</body>
</html>