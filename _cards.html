<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>cards</title>
</head>
<body>


<template id="simpleCard"
><article class="card @container relative w-full h-full bg-white shadow-lg group hover:shadow-xl transition-shadow ease-out durartion-300 overflow-hidden">
    <a href="#" title="Title" class="block w-full h-full cursor-pointer @mobile:flex">
        <div class="overflow-hidden @mobile:w-[200px] @mobile:mr-4">
            <div class="placeTemplate" data-template="image-16x9" data-component-title="Image (16x9 ratio)" data-display-code="0"></div>
        </div>
        <div class="flex flex-col gap-2 p-4">
            <div class="flex flex-col">
                <h3 class="subtitle font-tktypeBold">Headline lorem ipsum dolor sit amet</h3>
                <p class="small text-grey-80">TT.MM.JJJJ</p>
            </div>
            <p class="line-clamp-3 min-h-[70px] @mobile:min-h-0 small text-grey-80">
                Lorem ipsum dolor sit amet consectetur. Convallis in id dui velit dictum turpis turpis se.
            </p>
            <div class="font-tktypeBold text-primaryBlue group-hover:text-primaryBlue-140 flex flex-row gap-2 items-center">
                Mehr erfahren
                <svg width="18" height="18"><use href="assets/images/spritemap.svg#link-arrow-right"/></svg>
            </div>
        </div>
    </a>
</article></template>

<template id="downloadCard"
><article class="card relative w-full h-full bg-white shadow-lg group hover:shadow-xl transition-shadow ease-out durartion-300 overflow-hidden">
    <a href="#" title="Title" class="inline-block w-full h-full cursor-pointer">
        <div class="overflow-hidden">
            <div class="placeTemplate" data-template="image-16x9" data-component-title="Image (16x9 ratio)" data-display-code="0"></div>
        </div>
        <div class="flex flex-col gap-2 p-4">
            <div class="flex flex-col">
                <h3 class="subtitle font-tktypeBold">Headline lorem ipsum dolor sit amet</h3>
                <p class="small text-grey-80">TT.MM.JJJJ</p>
            </div>
            <p class="line-clamp-3 small text-grey-80">
                Lorem ipsum dolor sit amet consectetur. Convallis in id dui velit dictum turpis turpis se. Convallis in id dui velit dictum turpis turpis se.
            </p>
            <div class="font-tktypeBold text-primaryBlue group-hover:text-primaryBlue-140 flex flex-row gap-2 items-center">
                <svg width="16" height="17"><use href="assets/images/spritemap.svg#download-icon"/></svg>
                Download PDF
            </div>
        </div>
    </a>
</article></template>

<template id="cardTeaserRow" data-component-width="fullscreen"
><div class="corporate_grid">
    <div class="corporate_grid_full corporate_grid_flex3Cols">
        <div class="corporate_grid_flexCol">
            <!-- content start -->
            <div class="placeTemplate" data-template="downloadCard" data-component-title="Card"></div>
            <!-- content end -->
        </div>
        <div class="corporate_grid_flexCol">
            <!-- content start -->
            <div class="placeTemplate" data-template="simpleCard" data-component-title="Card"></div>
            <!-- content end -->
        </div>
        <div class="corporate_grid_flexCol">
            <!-- content start -->
            <div class="placeTemplate" data-template="simpleCard" data-component-title="Card"></div>
            <!-- content end -->
        </div>
    </div>
</div></template>

</body>
</html>